import { Manrope } from "next/font/google"
import { MdKeyboardArrowDown } from "react-icons/md";
import Products1 from "../../../../../public/products1.png";
import Products2 from "../../../../../public/products2.png";
import Products3 from "../../../../../public/products3.png";
import Image from "next/image";

type PropsType = {
    params: string | null
}

const manrope = Manrope({
    subsets:['latin']
})

const dresstorage = [
    {
        image:Products1,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products2,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products3,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products3,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products2,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products1,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products2,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products3,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products1,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    }
];

export default function Rightside({params}:PropsType){
    const customizeStar=(rate:number)=>{
        console.log(rate);
    }
    return(
        <>
        <div className="grid grid-cols-3 items-center">
            <div>
                <h2 className={`${manrope} font-bold text-[32px] capitalize text-black`}>
                    {params}
                </h2>
            </div>

            <div className="col-span-2 flex flex-row gap-x-3 justify-end">
                <div>
                    <p className={`${manrope} text-black/60 font-normal text-base capitalize`}>
                        Showing 1-10 of 100 products
                    </p>
                </div>

                <div className="flex flex-row">
                    <div>
                        <p className={`${manrope} text-black/60 font-normal text-base capitalize`}>
                            sort by:
                        </p>
                    </div>

                    <div>
                        <h5 className={`${manrope} text-black font-normal text-base capitalize flex flex-row items-center gap-x-1`}>
                            most popular<span><MdKeyboardArrowDown /></span>
                        </h5>
                    </div>
                </div>
            </div>
        </div>

        <div className="grid grid-cols-3 gap-x-5">
            {
                dresstorage.map((items,index)=>{
                    return <div key={index}>
                        <div className="w-full h-full bg-[#F0EEED]">
                            <Image src={items.image} fill alt="productImg" className="h-full w-full object-cover"/>
                        </div>

                        <div>
                            <h3>
                                {items.title}
                            </h3>

                            <div className="flex flex-row">
                                div
                            </div>
                        </div>
                    </div>
                })
            }
        </div>  
        </>
    )
}