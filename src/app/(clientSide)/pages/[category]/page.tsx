import { Manrope } from "next/font/google"
import { MdOutlineKeyboardArrowRight } from "react-icons/md"
import Leftside from "./leftside"
import Rightside from "./rightside"

type ParamsProps = {
    params:{
        category: string
    }
}

const manrope = Manrope({
    subsets:['latin']
});

export default function Category({params}:ParamsProps){
    return(
        <>
        <section className="mt-[140px]">
            <div className="flex flex-row gap-x-4 px-[100px]">
                <div className={`${manrope} text-[#000000]/60 capitalize font-normal flex flex-row items-center`}>
                    Home
                    <span>
                        <MdOutlineKeyboardArrowRight />
                    </span>
                </div>

                <div className={`${manrope} text-[#000000] capitalize font-normal`}>
                    {params.category}
                </div>
            </div>
        </section>

        <section className="mt-6">
            <div className="grid grid-cols-4 gap-x-5 px-[100px]">
                <div className="rounded-xl border border-[#dfe6e9] px-6 py-5">
                    <Leftside/>
                </div>

                <div className="col-span-3">
                    <Rightside params={params.category}/>
                </div>
            </div>
        </section>
        </>
    )
}